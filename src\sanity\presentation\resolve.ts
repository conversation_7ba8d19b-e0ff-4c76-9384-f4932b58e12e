import { defineLocations, DocumentLocation } from "sanity/presentation";

// Define a type for the document payload based on the select query
interface PostPayload {
  title?: string | null;
  slug?: string | null;
}

export const resolve = {
  locations: {
    post: defineLocations({
      // Select the title and the current string value of the slug
      select: {
        title: "title",
        slug: "slug.current",
      },
      // Resolve receives the selected fields in the 'doc' argument
      resolve: (doc: PostPayload | null) => {
        if (!doc) return { locations: [] };
        const resolvedLocations: DocumentLocation[] = [];

        // Add the specific post location only if a slug is present
        if (doc?.slug) {
          resolvedLocations.push({
            title: doc?.title || "Untitled", // Use document title or fallback
            href: `/post/${doc.slug}`,
          });
        }
        // Always include a link to the Home page
        resolvedLocations.push({ title: "Home", href: "/" });

        return {
          locations: resolvedLocations,
        };
      },
    }),
  },
};
