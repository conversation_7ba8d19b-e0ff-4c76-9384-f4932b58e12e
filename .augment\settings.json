{"mcpServers": {"Github": {"command": "npx", "args": ["-y", "@augmentcode/mcp-github"]}, "Linear": {"command": "npx", "args": ["-y", "@augmentcode/mcp-linear"]}, "Notion": {"command": "npx", "args": ["-y", "@augmentcode/mcp-notion"]}, "Context 7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"]}, "Sequential thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"]}, "MCP-Server-Qdrant": {"command": "uvx", "args": ["mcp-server-qdrant"], "env": {"QDRANT_URL": "https://7e03159b-e44c-42f9-9808-73fe5123dd0c.us-east4-0.gcp.cloud.qdrant.io:6333", "QDRANT_API_KEY": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhY2Nlc3MiOlt7ImNvbGxlY3Rpb24iOiJDbGluZV9TdG9yZSIsImFjY2VzcyI6InJ3In1dfQ.6_2gZQjpaUDqAdq2FUlVhk5pu_K7hqjXmozvqheDNTI", "COLLECTION_NAME": "Cline_Store", "EMBEDDING_MODEL": "sentence-transformers/all-MiniLM-L6-v2"}}, "BrowserBase": {"command": "node", "args": ["C:\\\\Users\\\\<USER>\\\\SoftwareStuff\\\\Cline\\\\MCP\\\\browserbase-mcp-server\\\\browserbase\\\\cli.js"], "env": {"BROWSERBASE_API_KEY": "bb_live_KPgPvaIcq5pyAB8BjDv5uz382uI", "BROWSERBASE_PROJECT_ID": "a52598ff-b771-4af9-af2d-b61e7a63b271"}}}, "Firecrawl-Scrape": {"command": "npx", "args": ["firecrawl-mcp", "C:\\\\Users\\\\<USER>\\\\SoftwareStuff\\\\Cline\\\\MCP\\\\firecrawl\\\\"], "env": {"FIRECRAWL_API_KEY": "fc-ad23aa2e77ce4692a1a2d62eb3e2e4ac"}}}