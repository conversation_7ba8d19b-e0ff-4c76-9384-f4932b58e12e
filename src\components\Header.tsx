"use client";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import "../app/globals.css";

export default function Header() {
  const [menuOpen, setMenuOpen] = useState(false);

  const navLinks = [
    { href: "/", label: "Home" },
    { href: "/about", label: "About" },
    { href: "/blog", label: "Blog" },
    { href: "/contact", label: "Contact" },
  ];

  return (
    <header className="bg p-6">
      <div className="container mx-auto flex items-center justify-between">
        <div className="flex items-center">
          <Image
            src="/chipsxp-logo-crop.jpg"
            alt="Company Logo"
            width={100}
            height={60}
            className="mr-3 rounded-[25px] border border-transparent"
          />
          <div className="text-center md:text-left">
            <h1 className="text-2xl font-bold">
              ChipsXP CMS of Gen AI Platforms
            </h1>
            <h2 className="text-lg md:text-xl">
              Research and Advance of Gen AI on Web Technology
            </h2>
          </div>
        </div>
        <div className="hidden md:flex items-center space-x-4">
          <nav>
            <ul className="flex space-x-4">
              {navLinks.map((link) => (
                <li key={link.href}>
                  <Link href={link.href} className="hover:underline">
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>
        <div className="md:hidden">
          <button onClick={() => setMenuOpen(!menuOpen)}>
            <svg
              className="w-8 h-8"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d={menuOpen ? "M6 18L18 6M6 6l12 12" : "M4 6h16M4 12h16m-7 6h7"}
              ></path>
            </svg>
          </button>
        </div>
      </div>
      {menuOpen && (
        <div className="md:hidden mt-4">
          <nav>
            <ul className="flex flex-col space-y-2">
              {navLinks.map((link) => (
                <li key={link.href}>
                  <Link href={link.href} className="bg-primary bg-primary-offset">
                    {link.label}
                  </Link>
                </li>
              ))}
            </ul>
          </nav>
        </div>
      )}
    </header>
  );
}
