{"name": "nextjs-sanity", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "extract": "npx sanity@latest schema extract", "generate": "npx sanity@latest typegen generate", "manage": "npx sanity manage"}, "dependencies": {"@sanity/icons": "^3.7.0", "@sanity/image-url": "^1.1.0", "@sanity/vision": "^3.92.0", "next": "15.3.3", "next-sanity": "^9.12.0", "postcss": "^8.5.6", "react": "^19.0.0", "react-dom": "^19.0.0", "react-is": "^19.1.0", "sanity": "^3.92.0", "styled-components": "^6.1.19"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "tailwindcss": "^4.1.10", "typescript": "^5"}}