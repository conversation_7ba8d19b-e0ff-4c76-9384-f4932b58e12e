import { POSTS_QUERYResult } from "../../sanity.types";

export function Posts ({ posts }: {posts: POSTS_QUERYResult}){
     return(
          <ul className="container mx-auto text-2xl grid grid-cols-1 divide-y">
               {posts.map((post) => (
                    <li key={post._id}>
                         <a className="block p-4 text-primary hover:text-foreground-offset"
                         href={`/posts/${post?.slug?.current}`}
                         >{post?.title}</a>
                    </li>
               ))}
          </ul>
     )
}