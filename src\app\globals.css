@import "tailwindcss";

:root {
  /* Light Mode */
  --color-background: #FEF3C7;
  --color-foreground: #111827;
  --color-background-offset: #F3F4F5;
  --color-foreground-offset: #374151;
  --color-primary: #FDE68A;
  --color-primary-offset: #E0F2E4;

  /* Dark Mode */
  --dark-color-background: #312e81;
  --dark-color-foreground: #e0e7ff; 
  --dark-color-background-offset: #4338ca;
  --dark-color-foreground-offset: #c7d2fe;
  --dark-color-primary: #818cf8;
  --dark-color-primary-offset: #6366f1;
}

@media (prefers-color-scheme: dark) {
  :root {
    --color-background: var(--dark-color-background);
    --color-foreground: var(--dark-color-foreground);
    --color-background-offset: var(--dark-color-background-offset);
    --color-foreground-offset: var(--dark-color-foreground-offset);
    --color-primary: var(--dark-color-primary);
    --color-primary-offset: var(--dark-color-primary-offset);
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --color-background: var(--color-background);
    --color-foreground: var(--color-foreground);
    --color-background-offset: var(--color-background-offset);
    --color-foreground-offset: var(--color-foreground-offset);
    --color-primary: var(--color-primary);
    --color-primary-offset: var(--color-primary-offset);
  }
}

body {
  color: var(--color-foreground);
  background-color: var(--color-background);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}