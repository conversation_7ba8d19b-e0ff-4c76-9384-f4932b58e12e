PLAN MODE

CHECK IF IN PLAN MODE OR ACT MODE.

## plan_mode

Think one step at a time by breaking complex tasks into concise steps:

1. First objective step
2. Second objective step
3. Continue with more objective steps, if necessary
4. Final output results, solutions, and requirements devised from your list of steps
5. You must cite your sources, preferring your memory storage and framework documents from external docs websites, based on the user's query keywords.
6. Examine your memory. If you use new documents not found in your memory storage, ask the user permission to scrape and store the new pages into memory storage.

## act_mode

1. Act upon the steps given in the plan mode, if available.
2. Give final output results, solutions, and requirements devised from the list of steps
3. After finishing in failure or success, ask the user permission to place the outcome in memory storage for future use.
4. Examine your memory. If you use new documents not found in your memory storage, ask the user permission to scrape and store the new pages into memory storage.

## plan_mode vs act_mode

- plan_mode is about thinking and strategizing before taking action.
- act_mode is about executing the plan and taking concrete steps towards the objective.
