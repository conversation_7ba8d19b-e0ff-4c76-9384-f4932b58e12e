import type { Config } from 'tailwindcss';
import typography from '@tailwindcss/typography';


const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
    './src/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        background: 'var(--color-background)',
        foreground: 'var(--color-foreground)',
        'background-offset': 'var(--color-background-offset)',
        'foreground-offset': 'var(--color-foreground-offset)',
        primary: 'var(--color-primary)',
        'primary-offset': 'var(--color-primary-offset)',
      },
      fontFamily: {
        sans: ['var(--font-geist-sans)', 'sans-serif'],
        mono: ['var(--font-geist-mono)', 'monospace'],
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: 'none',
            color: 'var(--color-foreground)',
            a: {
              color: 'var(--color-primary)',
              textDecoration: 'underline',
              '&:hover': {
                color: 'var(--color-primary-offset)',
              },
            },
            h1: {
              color: 'var(--color-foreground)',
            },
            h2: {
              color: 'var(--color-foreground)',
            },
            h3: {
              color: 'var(--color-foreground)',
            },
            h4: {
              color: 'var(--color-foreground)',
            },
            strong: {
              color: 'var(--color-foreground)',
            },
            code: {
              color: 'var(--color-foreground)',
            },
            blockquote: {
              color: 'var(--color-foreground)',
              borderLeftColor: 'var(--color-foreground)',
            },
          },
        },
      },
    },
  },
  plugins: [
    typography,
  ],
  darkMode: 'media', // Uses prefers-color-scheme
}

export default config